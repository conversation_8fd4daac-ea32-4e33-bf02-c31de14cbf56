import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Save, TestTube } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

const LineSettings = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [shopId, setShopId] = useState<string>("");
  
  const [settings, setSettings] = useState({
    channelId: "",
    channelSecret: "",
    redirectUri: "",
    newMemberWebhook: "",
    bookingSuccessWebhook: "",
    reminderWebhook: ""
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // 使用測試店家 ID
      const currentShopId = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
      setShopId(currentShopId);

      // 載入 LINE 設定
      const { data: lineSettings, error } = await supabase
        .from('line_settings')
        .select('*')
        .eq('shop_id', currentShopId)
        .maybeSingle();

      if (error) {
        console.error('Error loading LINE settings:', error);
        toast({
          title: "載入失敗",
          description: `無法載入 LINE 設定: ${error.message}`,
          variant: "destructive"
        });
        return;
      }

      if (lineSettings) {
        setSettings({
          channelId: lineSettings.channel_id || "",
          channelSecret: lineSettings.channel_secret || "",
          redirectUri: lineSettings.redirect_uri || "",
          newMemberWebhook: lineSettings.new_member_webhook || "",
          bookingSuccessWebhook: lineSettings.booking_success_webhook || "",
          reminderWebhook: lineSettings.reminder_webhook || ""
        });
      } else {
        // 沒有找到設定，使用預設值
        setSettings({
          channelId: "",
          channelSecret: "",
          redirectUri: "https://your-domain.com/auth/callback",
          newMemberWebhook: "",
          bookingSuccessWebhook: "",
          reminderWebhook: ""
        });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast({
        title: "載入失敗",
        description: `載入設定時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`,
        variant: "destructive"
      });
    }
  };

  const handleSave = async () => {
    if (!shopId) {
      toast({
        title: "錯誤",
        description: "找不到店家資訊",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('line_settings')
        .upsert({
          shop_id: shopId,
          channel_id: settings.channelId,
          channel_secret: settings.channelSecret,
          redirect_uri: settings.redirectUri,
          new_member_webhook: settings.newMemberWebhook,
          booking_success_webhook: settings.bookingSuccessWebhook,
          reminder_webhook: settings.reminderWebhook
        }, {
          onConflict: 'shop_id'
        });

      if (error) {
        console.error('Upsert error:', error);
        throw error;
      }

      toast({
        title: "設定已儲存",
        description: "LINE API 設定已成功更新",
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "儲存失敗",
        description: `無法儲存 LINE 設定: ${error instanceof Error ? error.message : '未知錯誤'}`,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTest = (webhookType: string) => {
    toast({
      title: "測試 Webhook",
      description: `正在測試 ${webhookType} Webhook...`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-rose-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Button 
            variant="outline" 
            onClick={() => navigate('/admin')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回後台
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">LINE API 設定</h1>
            <p className="text-gray-600">設定 LINE Login 與 Webhook 通知</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* LINE Login 設定 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                LINE Login 設定
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="channelId">Channel ID</Label>
                <Input
                  id="channelId"
                  value={settings.channelId}
                  onChange={(e) => setSettings({...settings, channelId: e.target.value})}
                  placeholder="輸入 LINE Login Channel ID"
                />
              </div>
              <div>
                <Label htmlFor="channelSecret">Channel Secret</Label>
                <Input
                  id="channelSecret"
                  type="password"
                  value={settings.channelSecret}
                  onChange={(e) => setSettings({...settings, channelSecret: e.target.value})}
                  placeholder="輸入 LINE Login Channel Secret"
                />
              </div>
              <div>
                <Label htmlFor="redirectUri">Redirect URI</Label>
                <Input
                  id="redirectUri"
                  value={settings.redirectUri}
                  onChange={(e) => setSettings({...settings, redirectUri: e.target.value})}
                  placeholder="https://yourdomain.com/auth/line/callback"
                />
              </div>
            </CardContent>
          </Card>

          {/* Webhook 設定 */}
          <Card>
            <CardHeader>
              <CardTitle>n8n Webhook 設定</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="newMemberWebhook">新會員註冊 Webhook</Label>
                <div className="flex gap-2">
                  <Textarea
                    id="newMemberWebhook"
                    value={settings.newMemberWebhook}
                    onChange={(e) => setSettings({...settings, newMemberWebhook: e.target.value})}
                    placeholder="https://your-n8n.com/webhook/new-member"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => handleTest("新會員註冊")}
                    className="flex items-center gap-2"
                  >
                    <TestTube className="h-4 w-4" />
                    測試
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  當新使用者首次透過 LINE 登入時觸發
                </p>
              </div>

              <div>
                <Label htmlFor="bookingSuccessWebhook">預約成功 Webhook</Label>
                <div className="flex gap-2">
                  <Textarea
                    id="bookingSuccessWebhook"
                    value={settings.bookingSuccessWebhook}
                    onChange={(e) => setSettings({...settings, bookingSuccessWebhook: e.target.value})}
                    placeholder="https://your-n8n.com/webhook/booking-success"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => handleTest("預約成功")}
                    className="flex items-center gap-2"
                  >
                    <TestTube className="h-4 w-4" />
                    測試
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  當使用者完成預約時觸發
                </p>
              </div>

              <div>
                <Label htmlFor="reminderWebhook">預約提醒 Webhook</Label>
                <div className="flex gap-2">
                  <Textarea
                    id="reminderWebhook"
                    value={settings.reminderWebhook}
                    onChange={(e) => setSettings({...settings, reminderWebhook: e.target.value})}
                    placeholder="https://your-n8n.com/webhook/reminder"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => handleTest("預約提醒")}
                    className="flex items-center gap-2"
                  >
                    <TestTube className="h-4 w-4" />
                    測試
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  預約前 24 小時自動發送提醒
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button 
              onClick={handleSave} 
              className="flex items-center gap-2"
              disabled={loading}
            >
              <Save className="h-4 w-4" />
              {loading ? "儲存中..." : "儲存設定"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LineSettings;