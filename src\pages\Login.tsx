import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, MessageCircle, Mail, Lock } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

const Login = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleEmailSignIn = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      toast({
        title: "登入成功",
        description: "歡迎回來！",
      });
      navigate("/services");
    } catch (error: any) {
      toast({
        title: "登入失敗",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEmailSignUp = async () => {
    setLoading(true);
    try {
      const redirectUrl = `${window.location.origin}/services`;
      
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl
        }
      });

      if (error) throw error;

      toast({
        title: "註冊成功",
        description: "請檢查您的電子郵件以完成驗證",
      });
    } catch (error: any) {
      toast({
        title: "註冊失敗", 
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // LINE登入功能（暫時導向信箱登入）
  const handleLineLogin = () => {
    toast({
      title: "LINE 登入",
      description: "LINE 登入功能開發中，請使用電子郵件登入",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-soft to-background flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => navigate(-1)}
            className="rounded-full"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-2xl font-bold text-foreground">登入</h1>
          <div className="w-10" />
        </div>

        {/* Login Tabs */}
        <Card className="p-6 card-shadow">
          <Tabs defaultValue="line" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="line">LINE 登入</TabsTrigger>
              <TabsTrigger value="email">信箱登入</TabsTrigger>
            </TabsList>

            <TabsContent value="line" className="space-y-4">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-primary rounded-full flex items-center justify-center">
                  <MessageCircle className="w-8 h-8 text-white" />
                </div>
                <p className="text-muted-foreground text-sm">
                  使用 LINE 帳號快速登入
                </p>
                <Button 
                  onClick={handleLineLogin}
                  className="w-full h-12 beauty-gradient text-white font-semibold rounded-full"
                  disabled={loading}
                >
                  <MessageCircle className="w-5 h-5 mr-2" />
                  使用 LINE 登入
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="email" className="space-y-4">
              <Tabs defaultValue="signin" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="signin">登入</TabsTrigger>
                  <TabsTrigger value="signup">註冊</TabsTrigger>
                </TabsList>

                <TabsContent value="signin" className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="signin-email">電子郵件</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          id="signin-email"
                          type="email"
                          placeholder="請輸入電子郵件"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="signin-password">密碼</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          id="signin-password"
                          type="password"
                          placeholder="請輸入密碼"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Button 
                      onClick={handleEmailSignIn}
                      className="w-full h-12 beauty-gradient text-white font-semibold rounded-full"
                      disabled={loading || !email || !password}
                    >
                      {loading ? "登入中..." : "登入"}
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="signup" className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="signup-email">電子郵件</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          id="signup-email"
                          type="email"
                          placeholder="請輸入電子郵件"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="signup-password">密碼</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          id="signup-password"
                          type="password"
                          placeholder="請設定密碼（至少6位）"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Button 
                      onClick={handleEmailSignUp}
                      className="w-full h-12 beauty-gradient text-white font-semibold rounded-full"
                      disabled={loading || !email || !password || password.length < 6}
                    >
                      {loading ? "註冊中..." : "註冊"}
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </TabsContent>
          </Tabs>
        </Card>

        {/* Footer */}
        <div className="text-center space-y-2">
          <p className="text-xs text-muted-foreground">
            繼續即表示您同意我們的服務條款和隱私政策
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;